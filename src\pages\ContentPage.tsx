import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Calendar, Clock, Star, Play, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import { mediaData } from '@/data/movies';
import { MediaItem } from '@/types/media';
import { scrollToTop } from '@/utils/scrollToTop';

export default function ContentPage() {
  const { id } = useParams<{ id: string }>();
  
  // Find the content item by ID
  const content: MediaItem | undefined = mediaData.find(item => item.id === id);

  if (!content) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Content Not Found</h1>
            <p className="text-muted-foreground mb-6">The requested content could not be found.</p>
            <Link to="/" className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Simulate secure video links (in production, this would come from the database)
  const hasVideoLinks = content.videoLinks || content.secureVideoLinks;

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <div className="relative h-[60vh] min-h-[400px] overflow-hidden">
          <img
            src={content.coverImage}
            alt={content.title}
            className="absolute inset-0 w-full h-full object-cover brightness-50"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
          
          <div className="relative z-10 h-full flex items-end">
            <div className="w-full max-w-7xl mx-auto px-4 pb-12">
              <div className="flex flex-col md:flex-row gap-8 items-end">
                {/* Poster */}
                <div className="flex-shrink-0">
                  <img
                    src={content.image}
                    alt={content.title}
                    className="w-48 h-72 object-cover rounded-lg shadow-2xl"
                  />
                </div>
                
                {/* Content Info */}
                <div className="flex-1 text-white">
                  <div className="mb-4">
                    <Badge variant="secondary" className="mb-2">
                      {content.type === 'movie' ? 'Movie' : 'Web Series'}
                    </Badge>
                    <h1 className="text-4xl md:text-6xl font-bold mb-2">{content.title}</h1>
                    <p className="text-lg md:text-xl text-gray-200 max-w-3xl">{content.description}</p>
                  </div>
                  
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>{content.runtime} min</span>
                      </div>
                    )}
                    {content.imdbRating && (
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span>{content.imdbRating}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {content.genres.map((genre) => (
                      <Badge key={genre} variant="outline" className="text-white border-white/30">
                        {genre}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex gap-4">
                    {hasVideoLinks && (
                      <Button size="lg" className="bg-primary hover:bg-primary/90">
                        <Play className="w-5 h-5 mr-2" />
                        Watch Now
                      </Button>
                    )}
                    <Button variant="outline" size="lg" className="text-white border-white/30 hover:bg-white/10">
                      <Download className="w-5 h-5 mr-2" />
                      Download
                    </Button>
                    <Button variant="outline" size="lg" className="text-white border-white/30 hover:bg-white/10">
                      <Share2 className="w-5 h-5 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Video Player */}
              {hasVideoLinks && (
                <Card>
                  <CardContent className="p-0">
                    <SecureVideoPlayer
                      encodedVideoLinks={content.secureVideoLinks}
                      legacyVideoLinks={content.videoLinks}
                      title={content.title}
                      showPlayerSelection={true}
                      className="w-full"
                    />
                  </CardContent>
                </Card>
              )}
              
              {/* Description */}
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-2xl font-bold mb-4">About</h2>
                  <p className="text-muted-foreground leading-relaxed">{content.description}</p>
                  
                  {content.tags && (
                    <div className="mt-6">
                      <h3 className="font-semibold mb-2">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {content.tags.split(',').map((tag, index) => (
                          <Badge key={index} variant="secondary">
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Episodes (for series) */}
              {content.type === 'series' && content.seasons && content.seasons.length > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-2xl font-bold mb-4">Episodes</h2>
                    <div className="space-y-4">
                      {content.seasons.map((season) => (
                        <div key={season.id}>
                          <h3 className="text-lg font-semibold mb-2">Season {season.seasonNumber}</h3>
                          <div className="grid gap-2">
                            {season.episodes.map((episode) => (
                              <div key={episode.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                                <div>
                                  <span className="font-medium">E{episode.episode}: {episode.title}</span>
                                  {episode.description && (
                                    <p className="text-sm text-muted-foreground">{episode.description}</p>
                                  )}
                                </div>
                                <Button variant="ghost" size="sm">
                                  <Play className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
            
            {/* Sidebar */}
            <div className="space-y-6">
              {/* Details */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-bold mb-4">Details</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Type</span>
                      <span className="capitalize">{content.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Year</span>
                      <span>{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Runtime</span>
                        <span>{content.runtime} minutes</span>
                      </div>
                    )}
                    {content.studio && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Studio</span>
                        <span>{content.studio}</span>
                      </div>
                    )}
                    {content.languages && content.languages.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Languages</span>
                        <span>{content.languages.join(', ')}</span>
                      </div>
                    )}
                    {content.quality && content.quality.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Quality</span>
                        <span>{content.quality.join(', ')}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* Back Button */}
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={() => {
                  window.history.back();
                  scrollToTop();
                }}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
